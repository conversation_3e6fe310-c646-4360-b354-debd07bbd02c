<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GTA Fencing Company - Fencing Services - Greater Toronto Area, Ontario</title>
    <meta name="description" content="Premier fence contractor in the Greater Toronto Area. Expert installation, repair, and maintenance for homeowners and businesses. Free quotes available.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Services Section */
        .services {
            padding: 4rem 0;
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .service-card a {
            color: #E94E1B;
            text-decoration: none;
            font-weight: 500;
        }

        .service-card a:hover {
            color: #D4A537;
        }

        /* Locations Section */
        .locations {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .location-item {
            padding: 1.5rem;
            background-color: #FAF1DF;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .location-item:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .location-item a {
            text-decoration: none;
            color: inherit;
            font-weight: 500;
        }

        /* Why Choose Us Section */
        .why-choose {
            padding: 4rem 0;
            background-color: #F4F4F4;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .feature-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .feature-item p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        .contact-form {
            max-width: 600px;
            margin: 0 auto;
            display: grid;
            gap: 1rem;
        }

        .form-group {
            display: grid;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 500;
            color: #FAF1DF;
        }

        .form-group input,
        .form-group textarea {
            padding: 1rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            background-color: #F4F4F4;
            color: #1B1B1F;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        a:focus,
        button:focus,
        input:focus,
        textarea:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="mailto:<EMAIL>" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>Your Premier Fence Contractor in the Greater Toronto Area</h1>
            <p class="hero-subtitle">As the GTA's leading fencing specialists, GTA Fencing Company provides expert installation, repair, and maintenance for homeowners and businesses across Toronto, Mississauga, Brampton, and the surrounding areas. We specialize in a wide range of fencing solutions designed for Southern Ontario's climate.</p>
            <a href="#contact" class="cta-button">Get Your Free Quote Today</a>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <h2 class="section-title">Our Fencing Services</h2>
            <div class="services-grid">
                <div class="service-card">
                    <h3><a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a></h3>
                    <p>Safe and stylish pool enclosures that meet all local safety by-laws.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a></h3>
                    <p>Custom wood fences for privacy, beauty, and natural appeal.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a></h3>
                    <p>Low-maintenance, durable vinyl fencing that won't rot or fade.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_chain-link-fencing_v1.html">Chain Link Fencing</a></h3>
                    <p>Affordable and secure chain link solutions for any property.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_wrought-iron-fencing_v1.html">Wrought Iron Fencing</a></h3>
                    <p>Elegant and strong ornamental fencing for discerning homeowners.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_perimeter-fencing_v1.html">Perimeter Fencing</a></h3>
                    <p>Commercial perimeter security for businesses and institutions.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_security-fencing_v1.html">Security Fencing</a></h3>
                    <p>High-security solutions for critical infrastructure protection.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_industrial-chain-link_v1.html">Industrial Chain Link</a></h3>
                    <p>Heavy-duty chain link for industrial and construction sites.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_fence-maintenance_v1.html">Fence Maintenance</a></h3>
                    <p>Professional maintenance to extend your fence's lifespan.</p>
                </div>
                <div class="service-card">
                    <h3><a href="gtafencing_fence-repair_v1.html">Fence Repair Services</a></h3>
                    <p>Fast and reliable repair services for damaged fences.</p>
                </div>
            </div>
            <div style="text-align: center;">
                <a href="gtafencing_services-hub_v1.html" class="cta-button">View All Services</a>
            </div>
        </div>
    </section>

    <!-- Locations Section -->
    <section class="locations" id="locations">
        <div class="container">
            <h2 class="section-title">Serving the Greater Toronto Area and Surrounding Communities</h2>
            <div class="locations-grid">
                <div class="location-item">
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_richmond-hill_v1.html">Richmond Hill</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_oakville_v1.html">Oakville</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_burlington_v1.html">Burlington</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_whitby_v1.html">Whitby</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_oshawa_v1.html">Oshawa</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose" id="why-choose">
        <div class="container">
            <h2 class="section-title">Why Choose GTA Fencing Company?</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <h3>Expert Installation</h3>
                    <p>Our certified team ensures every fence is built to last, meeting all local codes and standards.</p>
                </div>
                <div class="feature-item">
                    <h3>Quality Materials and Workmanship</h3>
                    <p>We source premium materials and stand behind our work, guaranteeing a durable and beautiful result.</p>
                </div>
                <div class="feature-item">
                    <h3>Free, No-Obligation Quotes</h3>
                    <p>We provide clear, detailed estimates with no hidden fees, helping you make an informed decision.</p>
                </div>
                <div class="feature-item">
                    <h3>Licensed and Insured</h3>
                    <p>Work with confidence knowing our team is fully licensed and insured for your complete protection.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="our-story" style="padding: 4rem 0; background-color: #FFFFFF;">
        <div class="container">
            <h2 class="section-title">Our Story of Quality and Commitment</h2>
            <div style="max-width: 800px; margin: 0 auto; text-align: center;">
                <p style="font-size: 1.1rem; line-height: 1.8; margin-bottom: 2rem;">
                    Founded with a commitment to excellence, GTA Fencing Company has been serving the Greater Toronto Area with pride and dedication. Our team of skilled professionals brings years of experience and a passion for quality craftsmanship to every project, whether it's a simple residential fence or a complex commercial installation.
                </p>
                <p style="font-size: 1.1rem; line-height: 1.8;">
                    We believe that a fence is more than just a boundary – it's an investment in your property's security, privacy, and value. That's why we're committed to using only the finest materials and proven installation techniques, backed by our comprehensive warranty and ongoing support.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <h2 class="section-title">Get Your Free Fencing Estimate Today</h2>
            <form class="contact-form" action="#" method="POST" aria-label="Contact form">
                <div class="form-group">
                    <label for="name">Full Name *</label>
                    <input type="text" id="name" name="name" required aria-required="true">
                </div>
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" required aria-required="true">
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" required aria-required="true">
                </div>
                <div class="form-group">
                    <label for="service">Service Needed</label>
                    <select id="service" name="service" style="padding: 1rem; border: none; border-radius: 5px; font-size: 1rem; background-color: #F4F4F4; color: #1B1B1F;">
                        <option value="">Select a service</option>
                        <option value="pool-fencing">Pool Fencing</option>
                        <option value="wood-fencing">Wood Fencing</option>
                        <option value="vinyl-fencing">Vinyl Fencing</option>
                        <option value="chain-link">Chain Link Fencing</option>
                        <option value="wrought-iron">Wrought Iron Fencing</option>
                        <option value="commercial">Commercial Fencing</option>
                        <option value="repair">Fence Repair</option>
                        <option value="maintenance">Fence Maintenance</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="message">Project Details</label>
                    <textarea id="message" name="message" placeholder="Tell us about your fencing project..."></textarea>
                </div>
                <button type="submit" class="cta-button">Request Free Quote</button>
            </form>
            <div style="margin-top: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 1rem;">Or call us directly:</p>
                <a href="tel:6475577550" class="phone" style="font-size: 1.5rem;">(*************</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:6475577550">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a>
                    <a href="gtafencing_chain-link-fencing_v1.html">Chain Link Fencing</a>
                    <a href="gtafencing_wrought-iron-fencing_v1.html">Wrought Iron Fencing</a>
                </div>
                <div class="footer-section">
                    <h3>Service Areas</h3>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <a href="https://www.facebook.com/gtafencing" target="_blank" rel="noopener">Facebook</a>
                    <a href="https://x.com/FencingGta" target="_blank" rel="noopener">Twitter</a>
                    <a href="https://www.youtube.com/@gtafencecompany" target="_blank" rel="noopener">YouTube</a>
                    <a href="https://www.pinterest.com/gtafencing/" target="_blank" rel="noopener">Pinterest</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Form validation and submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic form validation
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();

            if (!name || !email || !phone) {
                alert('Please fill in all required fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Phone validation (basic)
            const phoneRegex = /^[\d\s\-\(\)\+]+$/;
            if (!phoneRegex.test(phone)) {
                alert('Please enter a valid phone number.');
                return;
            }

            // Success message
            alert('Thank you for your inquiry! We will contact you within 24 hours to schedule your free estimate.');

            // Reset form
            this.reset();
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('mobile-open');
        }

        // Add loading animation to CTA buttons
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function() {
                if (this.type === 'submit') {
                    this.style.opacity = '0.7';
                    this.textContent = 'Sending...';
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe service cards and feature items
        document.querySelectorAll('.service-card, .feature-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>
