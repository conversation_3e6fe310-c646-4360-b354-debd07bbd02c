<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | GTA Fencing Company - Free Quotes Available</title>
    <meta name="description" content="Contact GTA Fencing Company for your free estimate. Call (************* or request a quote online. Serving the Greater Toronto Area.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 50px;
            width: auto;
            max-width: 200px;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Contact Methods Section */
        .contact-methods {
            padding: 4rem 0;
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .method-card {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-top: 4px solid #D4A537;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .method-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .method-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            color: #1B1B1F;
        }

        .method-card .contact-detail {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            margin-bottom: 1rem;
        }

        /* Contact Form Section */
        .contact-form-section {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #FAF1DF;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .contact-form {
            display: grid;
            gap: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .form-group {
            display: grid;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: #273043;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 1rem;
            border: 2px solid #D4A537;
            border-radius: 8px;
            font-size: 1rem;
            background-color: #FFFFFF;
            color: #1B1B1F;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E94E1B;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        /* Service Areas */
        .service-areas {
            padding: 4rem 0;
            background-color: #F4F4F4;
        }

        .areas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .area-item {
            background-color: #FFFFFF;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .area-item:hover {
            background-color: #D4A537;
            color: #FFFFFF;
            transform: translateY(-3px);
        }

        .area-item a {
            text-decoration: none;
            color: inherit;
            font-weight: 500;
        }

        /* Business Hours */
        .business-hours {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .hours-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #FAF1DF;
            padding: 2.5rem;
            border-radius: 10px;
            text-align: center;
        }

        .hours-grid {
            display: grid;
            gap: 1rem;
            margin-top: 2rem;
        }

        .hours-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #D4A537;
        }

        .hours-row:last-child {
            border-bottom: none;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-container {
                padding: 2rem;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus,
        input:focus,
        textarea:focus,
        select:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="gtafencing_homepage_v1.html">
                        <img src="Images/gta-fencing-logo.png" alt="GTA Fencing Company - Professional Fence Installation Toronto" />
                    </a>
                </div>
                <div class="contact-info">
                    <a href="tel:**********" class="phone" aria-label="Call us at ************">(*************</a>
                    <span class="cta-button" style="background-color: #D4A537;">Get Free Quote</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="#contact" style="background-color: #D4A537; color: #FFFFFF;">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Get Your Free Fencing Estimate Today</h1>
            <p class="hero-subtitle">Ready to enhance your property with professional fencing? Contact GTA Fencing Company for expert advice, quality installation, and exceptional service. We're here to help bring your vision to life.</p>
            <a href="#contact-form" class="cta-button">Request Free Quote</a>
        </div>
    </section>

    <!-- Contact Methods Section -->
    <section class="contact-methods" id="contact">
        <div class="container">
            <h2 class="section-title">How to Reach Us</h2>
            <div class="methods-grid">
                <div class="method-card">
                    <h3>📞 Call Us</h3>
                    <p>Speak directly with our fencing experts for immediate assistance and answers to your questions.</p>
                    <div class="contact-detail">
                        <a href="tel:**********" style="color: inherit; text-decoration: none;">(*************</a>
                    </div>
                    <p style="font-size: 0.9rem; color: #273043;">Available Monday - Saturday, 8 AM - 6 PM</p>
                </div>

                <div class="method-card">
                    <h3>✉️ Email Us</h3>
                    <p>Send us your project details and we'll respond with a detailed estimate within 24 hours.</p>
                    <div class="contact-detail">
                        <a href="mailto:<EMAIL>" style="color: inherit; text-decoration: none;"><EMAIL></a>
                    </div>
                    <p style="font-size: 0.9rem; color: #273043;">We respond to all emails within 24 hours</p>
                </div>

                <div class="method-card">
                    <h3>📋 Online Quote</h3>
                    <p>Fill out our detailed form below for a comprehensive estimate tailored to your specific needs.</p>
                    <div class="contact-detail">Free & No Obligation</div>
                    <a href="#contact-form" class="cta-button" style="font-size: 1rem; padding: 0.8rem 1.5rem;">Get Quote Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="contact-form-section" id="contact-form">
        <div class="container">
            <h2 class="section-title">Request Your Free Estimate</h2>
            <div class="form-container">
                <form class="contact-form" action="#" method="POST" aria-label="Contact form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required aria-required="true">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required aria-required="true">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required aria-required="true">
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required aria-required="true">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">City/Location *</label>
                            <select id="city" name="city" required aria-required="true">
                                <option value="">Select your city</option>
                                <option value="toronto">Toronto</option>
                                <option value="mississauga">Mississauga</option>
                                <option value="brampton">Brampton</option>
                                <option value="vaughan">Vaughan</option>
                                <option value="markham">Markham</option>
                                <option value="richmond-hill">Richmond Hill</option>
                                <option value="oakville">Oakville</option>
                                <option value="burlington">Burlington</option>
                                <option value="whitby">Whitby</option>
                                <option value="oshawa">Oshawa</option>
                                <option value="other">Other GTA Location</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="propertyType">Property Type</label>
                            <select id="propertyType" name="propertyType">
                                <option value="">Select property type</option>
                                <option value="residential">Residential</option>
                                <option value="commercial">Commercial</option>
                                <option value="industrial">Industrial</option>
                                <option value="institutional">Institutional</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="service">Service Needed *</label>
                        <select id="service" name="service" required aria-required="true">
                            <option value="">Select a service</option>
                            <option value="pool-fencing">Pool Fencing</option>
                            <option value="wood-fencing">Wood Fencing</option>
                            <option value="vinyl-fencing">Vinyl Fencing</option>
                            <option value="chain-link">Chain Link Fencing</option>
                            <option value="wrought-iron">Wrought Iron Fencing</option>
                            <option value="perimeter-fencing">Perimeter Fencing</option>
                            <option value="security-fencing">Security Fencing</option>
                            <option value="industrial-chain-link">Industrial Chain Link</option>
                            <option value="fence-repair">Fence Repair</option>
                            <option value="fence-maintenance">Fence Maintenance</option>
                            <option value="consultation">Consultation Only</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="fenceLength">Approximate Fence Length (feet)</label>
                            <input type="number" id="fenceLength" name="fenceLength" placeholder="e.g., 100">
                        </div>
                        <div class="form-group">
                            <label for="fenceHeight">Desired Fence Height</label>
                            <select id="fenceHeight" name="fenceHeight">
                                <option value="">Select height</option>
                                <option value="3-feet">3 feet</option>
                                <option value="4-feet">4 feet</option>
                                <option value="5-feet">5 feet</option>
                                <option value="6-feet">6 feet</option>
                                <option value="8-feet">8 feet</option>
                                <option value="custom">Custom Height</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="timeline">Preferred Timeline</label>
                        <select id="timeline" name="timeline">
                            <option value="">Select timeline</option>
                            <option value="asap">As soon as possible</option>
                            <option value="1-month">Within 1 month</option>
                            <option value="2-3-months">2-3 months</option>
                            <option value="spring">Next spring</option>
                            <option value="flexible">Flexible</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="budget">Budget Range (Optional)</label>
                        <select id="budget" name="budget">
                            <option value="">Select budget range</option>
                            <option value="under-2000">Under $2,000</option>
                            <option value="2000-5000">$2,000 - $5,000</option>
                            <option value="5000-10000">$5,000 - $10,000</option>
                            <option value="10000-plus">$10,000+</option>
                            <option value="discuss">Prefer to discuss</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="message">Project Details & Special Requirements</label>
                        <textarea id="message" name="message" placeholder="Please describe your fencing project, any specific requirements, site conditions, or questions you have..."></textarea>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <label for="newsletter">I'd like to receive maintenance tips and fencing advice via email</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="consent" name="consent" required>
                        <label for="consent">I consent to being contacted about my fencing project *</label>
                    </div>

                    <button type="submit" class="cta-button" style="width: 100%; margin-top: 1rem;">Request Free Estimate</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Service Areas Section -->
    <section class="service-areas">
        <div class="container">
            <h2 class="section-title">We Serve These Areas</h2>
            <div class="areas-grid">
                <div class="area-item">
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_richmond-hill_v1.html">Richmond Hill</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_oakville_v1.html">Oakville</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_burlington_v1.html">Burlington</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_whitby_v1.html">Whitby</a>
                </div>
                <div class="area-item">
                    <a href="gtafencing_oshawa_v1.html">Oshawa</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Business Hours Section -->
    <section class="business-hours">
        <div class="container">
            <h2 class="section-title">Business Hours</h2>
            <div class="hours-container">
                <h3 style="color: #273043; margin-bottom: 1rem;">When We're Available</h3>
                <div class="hours-grid">
                    <div class="hours-row">
                        <span><strong>Monday - Friday</strong></span>
                        <span>8:00 AM - 6:00 PM</span>
                    </div>
                    <div class="hours-row">
                        <span><strong>Saturday</strong></span>
                        <span>9:00 AM - 4:00 PM</span>
                    </div>
                    <div class="hours-row">
                        <span><strong>Sunday</strong></span>
                        <span>Closed</span>
                    </div>
                    <div class="hours-row">
                        <span><strong>Emergency Repairs</strong></span>
                        <span>Available 24/7</span>
                    </div>
                </div>
                <p style="margin-top: 1.5rem; font-style: italic; color: #273043;">
                    We offer flexible scheduling including evenings and weekends for consultations by appointment.
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:**********">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p style="margin-top: 1rem;">Licensed & Insured</p>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <a href="gtafencing_services-hub_v1.html">All Services</a>
                    <a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a>
                    <a href="gtafencing_fence-repair_v1.html">Fence Repair</a>
                </div>
                <div class="footer-section">
                    <h3>Service Areas</h3>
                    <a href="gtafencing_location-hub_v1.html">All Locations</a>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                </div>
                <div class="footer-section">
                    <h3>Resources</h3>
                    <a href="gtafencing_blog_v1.html">Fencing Resources</a>
                    <a href="#">Maintenance Guide</a>
                    <a href="#">Municipal By-Laws</a>
                    <a href="#">Design Gallery</a>
                    <a href="#">FAQ</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Form validation and submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const firstName = formData.get('firstName').trim();
            const lastName = formData.get('lastName').trim();
            const email = formData.get('email').trim();
            const phone = formData.get('phone').trim();
            const city = formData.get('city');
            const service = formData.get('service');
            const consent = formData.get('consent');

            // Validation
            if (!firstName || !lastName || !email || !phone || !city || !service) {
                alert('Please fill in all required fields.');
                return;
            }

            if (!consent) {
                alert('Please consent to being contacted about your project.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Phone validation
            const phoneRegex = /^[\d\s\-\(\)\+]+$/;
            if (!phoneRegex.test(phone)) {
                alert('Please enter a valid phone number.');
                return;
            }

            // Success message
            alert('Thank you for your request! We will contact you within 24 hours to schedule your free estimate.');

            // Reset form
            this.reset();
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form field enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-format phone number
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length >= 6) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                } else if (value.length >= 3) {
                    value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
                }
                this.value = value;
            });

            // Dynamic service suggestions based on property type
            const propertyType = document.getElementById('propertyType');
            const service = document.getElementById('service');

            propertyType.addEventListener('change', function() {
                const selectedType = this.value;
                const serviceOptions = service.querySelectorAll('option');

                serviceOptions.forEach(option => {
                    if (option.value === '') return; // Keep default option

                    if (selectedType === 'residential') {
                        option.style.display = ['pool-fencing', 'wood-fencing', 'vinyl-fencing', 'chain-link', 'wrought-iron', 'fence-repair', 'fence-maintenance'].includes(option.value) ? 'block' : 'none';
                    } else if (selectedType === 'commercial' || selectedType === 'industrial') {
                        option.style.display = ['perimeter-fencing', 'security-fencing', 'industrial-chain-link', 'chain-link', 'fence-repair', 'fence-maintenance'].includes(option.value) ? 'block' : 'none';
                    } else {
                        option.style.display = 'block';
                    }
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe cards and form
        document.querySelectorAll('.method-card, .area-item, .form-container').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>
