<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pool Fencing Installation | GTA Fencing Company</title>
    <meta name="description" content="Professional pool fence installation in the GTA. Safety-compliant pool enclosures that meet local by-laws. Free estimates available.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .content-section:nth-child(even) {
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .option-card {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 4px solid #D4A537;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left-color: #E94E1B;
        }

        .option-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .option-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            color: #1B1B1F;
        }

        .option-card .features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .option-card .features li {
            padding: 0.3rem 0;
            color: #273043;
            position: relative;
            padding-left: 1.5rem;
        }

        .option-card .features li:before {
            content: "✓";
            color: #D4A537;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        /* Process Steps */
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .step-card {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .step-number {
            background-color: #E94E1B;
            color: #FFFFFF;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .step-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .step-card p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Locations Grid */
        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .location-item {
            background-color: #FFFFFF;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .location-item:hover {
            background-color: #D4A537;
            color: #FFFFFF;
            transform: translateY(-3px);
        }

        .location-item a {
            text-decoration: none;
            color: inherit;
            font-weight: 500;
        }

        /* Why Choose Section */
        .why-choose {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            background-color: #FAF1DF;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }

        .feature-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .feature-item p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .options-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Greater Toronto Area's Pool Fencing for Enhanced Safety and Style</h1>
            <p class="hero-subtitle">GTA Fencing Company provides professional pool fence installation across the Greater Toronto Area, ensuring your pool meets local safety by-laws while enhancing your backyard's aesthetic. Our pool fences are built with durable, high-quality materials designed for safety and longevity.</p>
            <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Pool Fence Quote</a>
        </div>
    </section>

    <!-- Pool Fencing Options Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Pool Fencing Options from GTA Fencing Company</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                We offer a variety of pool fence styles to match your home's look and meet your safety needs. Each option provides a secure barrier to protect children and pets.
            </p>
            <div class="options-grid">
                <div class="option-card">
                    <h3>Aluminum Pool Fencing</h3>
                    <p>Durable, low-maintenance aluminum fencing that won't rust or corrode, perfect for pool environments.</p>
                    <ul class="features">
                        <li>Powder-coated finish for longevity</li>
                        <li>Self-closing, self-latching gates</li>
                        <li>Multiple height options available</li>
                        <li>Meets all safety code requirements</li>
                        <li>Available in black, white, and bronze</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Glass Panel Pool Fencing</h3>
                    <p>Elegant tempered glass panels that provide safety without obstructing your pool view.</p>
                    <ul class="features">
                        <li>Tempered safety glass construction</li>
                        <li>Frameless or semi-frameless options</li>
                        <li>Stainless steel hardware</li>
                        <li>Easy to clean and maintain</li>
                        <li>Premium aesthetic appeal</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Vinyl Pool Fencing</h3>
                    <p>Cost-effective vinyl fencing that combines safety with style and requires minimal maintenance.</p>
                    <ul class="features">
                        <li>Won't fade, crack, or peel</li>
                        <li>Easy to clean with soap and water</li>
                        <li>Multiple color and style options</li>
                        <li>Secure locking gate systems</li>
                        <li>Excellent value for money</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Removable Mesh Pool Fencing</h3>
                    <p>Temporary safety solution that can be removed when not needed, perfect for seasonal use.</p>
                    <ul class="features">
                        <li>Lightweight yet strong mesh material</li>
                        <li>Easy installation and removal</li>
                        <li>Drill-in or deck-mounted options</li>
                        <li>Compact storage when not in use</li>
                        <li>Ideal for rental properties</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Process Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">How Our Pool Fencing Process Works</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                Our streamlined process ensures a seamless experience from start to finish, delivering a safe and beautiful pool enclosure.
            </p>
            <div class="process-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3>Initial Consultation</h3>
                    <p><strong>On-site measurement and discussion</strong> → A precise, no-obligation quote tailored to your property.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3>By-Law Compliance Check</h3>
                    <p><strong>Review of local municipal pool enclosure regulations</strong> → A design that is guaranteed to pass inspection.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3>Material Selection</h3>
                    <p><strong>Guided choice of aluminum, glass, or vinyl</strong> → The perfect balance of safety, style, and budget for your home.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3>Expert Installation</h3>
                    <p><strong>Professional, efficient construction with minimal disruption</strong> → A secure, long-lasting fence installed correctly the first time.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">5</div>
                    <h3>Final Inspection</h3>
                    <p><strong>A walkthrough with you to ensure complete satisfaction</strong> → Confidence and peace of mind in your new pool enclosure.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Areas Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Pool Fencing Service Areas Throughout the Greater Toronto Area</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                We bring professional pool fence installation services to communities across the GTA and beyond.
            </p>
            <div class="locations-grid">
                <div class="location-item">
                    <h3><a href="gtafencing_toronto_v1.html">Pool Fencing in Toronto</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_vaughan_v1.html">Pool Fencing in Vaughan</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_richmond-hill_v1.html">Pool Fencing in Richmond Hill</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_markham_v1.html">Pool Fencing in Markham</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_mississauga_v1.html">Pool Fencing in Mississauga</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_brampton_v1.html">Pool Fencing in Brampton</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_oakville_v1.html">Pool Fencing in Oakville</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_burlington_v1.html">Pool Fencing in Burlington</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_whitby_v1.html">Pool Fencing in Whitby</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_oshawa_v1.html">Pool Fencing in Oshawa</a></h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose">
        <div class="container">
            <h2 class="section-title">Why Choose GTA Fencing Company For Your Pool Fencing?</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                Our commitment to safety and quality makes us the trusted choice for pool enclosures.
            </p>
            <div class="features-grid">
                <div class="feature-item">
                    <h3>Uncompromising Safety Standards</h3>
                    <p>Every pool fence we install meets or exceeds local safety codes and regulations, ensuring maximum protection for your family.</p>
                </div>
                <div class="feature-item">
                    <h3>Quality Materials and Workmanship</h3>
                    <p>We use only premium materials and proven installation techniques, backed by comprehensive warranties for your peace of mind.</p>
                </div>
                <div class="feature-item">
                    <h3>Licensed, Insured, and By-Law Compliant</h3>
                    <p>Our fully licensed and insured team ensures your pool fence installation is compliant with all municipal requirements.</p>
                </div>
                <div class="feature-item">
                    <h3>Expert Local Knowledge</h3>
                    <p>Deep understanding of GTA municipal by-laws and regulations ensures your pool fence passes inspection the first time.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Services Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Explore Our Full Range of Fencing Services</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                While you're here, explore the other ways we can secure and beautify your property.
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Residential Fencing Solutions</h3>
                    <p style="margin-bottom: 1.5rem;">Privacy fences, decorative fencing, and property boundary solutions.</p>
                    <a href="gtafencing_services-hub_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">View All Residential Services →</a>
                </div>
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Commercial and Industrial Fencing</h3>
                    <p style="margin-bottom: 1.5rem;">Security fencing, perimeter solutions, and access control systems.</p>
                    <a href="gtafencing_services-hub_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">View Commercial Services →</a>
                </div>
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Fence Maintenance and Repair</h3>
                    <p style="margin-bottom: 1.5rem;">Keep your existing fences in perfect condition with our maintenance services.</p>
                    <a href="gtafencing_fence-maintenance_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">Learn About Maintenance →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title">Ready to Secure Your Pool Area?</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Contact us today for a free, no-obligation estimate on your pool fencing project.</p>
            <div style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
                <a href="tel:6475577550" class="cta-button">Call (*************</a>
                <a href="gtafencing_contact_v1.html" class="cta-button">Request Pool Fence Quote</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:6475577550">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h3>Pool Fencing Services</h3>
                    <a href="#">Aluminum Pool Fencing</a>
                    <a href="#">Glass Panel Pool Fencing</a>
                    <a href="#">Vinyl Pool Fencing</a>
                    <a href="#">Removable Mesh Fencing</a>
                    <a href="#">Pool Safety Inspections</a>
                </div>
                <div class="footer-section">
                    <h3>Other Services</h3>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a>
                    <a href="gtafencing_chain-link-fencing_v1.html">Chain Link Fencing</a>
                    <a href="gtafencing_fence-repair_v1.html">Fence Repair</a>
                    <a href="gtafencing_fence-maintenance_v1.html">Fence Maintenance</a>
                </div>
                <div class="footer-section">
                    <h3>Service Areas</h3>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured pool fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe cards and elements
        document.querySelectorAll('.option-card, .step-card, .location-item, .feature-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add hover effects to option cards
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderLeftColor = '#E94E1B';
            });

            card.addEventListener('mouseleave', function() {
                this.style.borderLeftColor = '#D4A537';
            });
        });

        // Pool fence calculator
        function createPoolFenceCalculator() {
            const calculatorSection = document.createElement('section');
            calculatorSection.className = 'content-section';
            calculatorSection.style.backgroundColor = '#FAF1DF';

            calculatorSection.innerHTML = `
                <div class="container">
                    <h2 class="section-title">Pool Fence Cost Calculator</h2>
                    <div style="max-width: 600px; margin: 0 auto; background-color: #FFFFFF; padding: 2.5rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <div style="display: grid; gap: 1.5rem;">
                            <div>
                                <label for="fenceType" style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Fence Type:</label>
                                <select id="fenceType" style="width: 100%; padding: 0.8rem; border: 2px solid #D4A537; border-radius: 5px;">
                                    <option value="aluminum">Aluminum Pool Fencing</option>
                                    <option value="glass">Glass Panel Fencing</option>
                                    <option value="vinyl">Vinyl Pool Fencing</option>
                                    <option value="mesh">Removable Mesh Fencing</option>
                                </select>
                            </div>
                            <div>
                                <label for="perimeter" style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Pool Perimeter (feet):</label>
                                <input type="number" id="perimeter" placeholder="e.g., 80" style="width: 100%; padding: 0.8rem; border: 2px solid #D4A537; border-radius: 5px;">
                            </div>
                            <div>
                                <label for="gates" style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Number of Gates:</label>
                                <select id="gates" style="width: 100%; padding: 0.8rem; border: 2px solid #D4A537; border-radius: 5px;">
                                    <option value="1">1 Gate</option>
                                    <option value="2">2 Gates</option>
                                    <option value="3">3+ Gates</option>
                                </select>
                            </div>
                            <button onclick="calculatePoolFence()" style="background-color: #E94E1B; color: #FFFFFF; padding: 1rem; border: none; border-radius: 8px; font-weight: 600; cursor: pointer;">Calculate Estimate</button>
                            <div id="calculatorResult" style="display: none; background-color: #FAF1DF; padding: 1.5rem; border-radius: 8px; text-align: center;">
                                <h3 style="color: #273043; margin-bottom: 1rem;">Estimated Cost Range</h3>
                                <p id="costRange" style="font-size: 1.2rem; font-weight: 600; color: #E94E1B;"></p>
                                <p style="font-size: 0.9rem; color: #273043; margin-top: 1rem;">*Estimate includes materials and installation. Final cost may vary based on site conditions.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Insert before the contact section
            const contactSection = document.querySelector('.contact');
            contactSection.parentNode.insertBefore(calculatorSection, contactSection);
        }

        function calculatePoolFence() {
            const fenceType = document.getElementById('fenceType').value;
            const perimeter = parseInt(document.getElementById('perimeter').value);
            const gates = parseInt(document.getElementById('gates').value);

            if (!perimeter || perimeter <= 0) {
                alert('Please enter a valid perimeter length.');
                return;
            }

            const baseCosts = {
                aluminum: 45,
                glass: 85,
                vinyl: 35,
                mesh: 25
            };

            const gateCosts = {
                aluminum: 350,
                glass: 650,
                vinyl: 280,
                mesh: 180
            };

            const baseCost = baseCosts[fenceType] * perimeter;
            const gateCost = gateCosts[fenceType] * gates;
            const totalLow = baseCost + gateCost;
            const totalHigh = totalLow * 1.3; // 30% variance for complexity

            document.getElementById('costRange').textContent = `$${totalLow.toLocaleString()} - $${totalHigh.toLocaleString()}`;
            document.getElementById('calculatorResult').style.display = 'block';
        }

        // Initialize calculator when page loads
        document.addEventListener('DOMContentLoaded', createPoolFenceCalculator);
    </script>
</body>
</html>
