<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Areas | GTA Fencing Company - Greater Toronto Area Locations</title>
    <meta name="description" content="GTA Fencing Company serves Toronto, Mississauga, Brampton, Vaughan, Markham, and surrounding areas. Find local fencing services in your community.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Locations Section */
        .locations {
            padding: 4rem 0;
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .location-card {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
            border-top: 4px solid #D4A537;
        }

        .location-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-top-color: #E94E1B;
        }

        .location-card h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .location-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            color: #1B1B1F;
        }

        .location-card .neighborhoods {
            list-style: none;
            margin-bottom: 1.5rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }

        .location-card .neighborhoods li {
            background-color: #FAF1DF;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #273043;
        }

        .location-link {
            color: #E94E1B;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .location-link:hover {
            color: #D4A537;
        }

        .location-link:after {
            content: "→";
            transition: transform 0.3s ease;
        }

        .location-link:hover:after {
            transform: translateX(5px);
        }

        /* Service Coverage */
        .service-coverage {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .coverage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .coverage-item {
            background-color: #FAF1DF;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }

        .coverage-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .coverage-item p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .locations-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="#locations" style="background-color: #D4A537; color: #FFFFFF;">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Serving the Greater Toronto Area</h1>
            <p class="hero-subtitle">GTA Fencing Company proudly serves homeowners and businesses across the Greater Toronto Area and surrounding communities. Find expert fencing services in your local area with our comprehensive coverage throughout Southern Ontario.</p>
            <a href="#locations" class="cta-button">Find Your Location</a>
        </div>
    </section>

    <!-- Primary Locations Section -->
    <section class="locations" id="locations">
        <div class="container">
            <h2 class="section-title">Our Primary Service Areas</h2>
            <div class="locations-grid">
                <div class="location-card">
                    <h3>Toronto</h3>
                    <p>Canada's largest city with diverse neighborhoods from downtown to the suburbs.</p>
                    <ul class="neighborhoods">
                        <li>North York</li>
                        <li>Scarborough</li>
                        <li>Etobicoke</li>
                        <li>The Beaches</li>
                        <li>Leaside</li>
                        <li>Rosedale</li>
                    </ul>
                    <a href="gtafencing_toronto_v1.html" class="location-link">View Toronto Services</a>
                </div>

                <div class="location-card">
                    <h3>Mississauga</h3>
                    <p>Canada's seventh-largest city with waterfront communities and family neighborhoods.</p>
                    <ul class="neighborhoods">
                        <li>Port Credit</li>
                        <li>Streetsville</li>
                        <li>Clarkson</li>
                        <li>Erin Mills</li>
                        <li>Lorne Park</li>
                        <li>Cooksville</li>
                    </ul>
                    <a href="gtafencing_mississauga_v1.html" class="location-link">View Mississauga Services</a>
                </div>

                <div class="location-card">
                    <h3>Brampton</h3>
                    <p>Dynamic city with growing communities and diverse housing developments.</p>
                    <ul class="neighborhoods">
                        <li>Bramalea</li>
                        <li>Heart Lake</li>
                        <li>Mount Pleasant</li>
                        <li>Springdale</li>
                        <li>Downtown</li>
                        <li>Fletcher's Meadow</li>
                    </ul>
                    <a href="gtafencing_brampton_v1.html" class="location-link">View Brampton Services</a>
                </div>

                <div class="location-card">
                    <h3>Vaughan</h3>
                    <p>Premier suburban communities with new developments and established neighborhoods.</p>
                    <ul class="neighborhoods">
                        <li>Woodbridge</li>
                        <li>Maple</li>
                        <li>Concord</li>
                        <li>Kleinburg</li>
                        <li>Thornhill</li>
                        <li>Sonoma Heights</li>
                    </ul>
                    <a href="gtafencing_vaughan_v1.html" class="location-link">View Vaughan Services</a>
                </div>

                <div class="location-card">
                    <h3>Markham</h3>
                    <p>Diverse communities from historic Unionville to modern Cornell developments.</p>
                    <ul class="neighborhoods">
                        <li>Unionville</li>
                        <li>Thornhill</li>
                        <li>Cornell</li>
                        <li>Angus Glen</li>
                        <li>Berczy Village</li>
                        <li>Milliken Mills</li>
                    </ul>
                    <a href="gtafencing_markham_v1.html" class="location-link">View Markham Services</a>
                </div>

                <div class="location-card">
                    <h3>Richmond Hill</h3>
                    <p>Beautiful communities with mature trees and Oak Ridges Moraine properties.</p>
                    <ul class="neighborhoods">
                        <li>Oak Ridges</li>
                        <li>Mill Pond</li>
                        <li>Jefferson</li>
                        <li>Bayview Hill</li>
                        <li>Langstaff</li>
                        <li>Doncrest</li>
                    </ul>
                    <a href="gtafencing_richmond-hill_v1.html" class="location-link">View Richmond Hill Services</a>
                </div>

                <div class="location-card">
                    <h3>Oakville</h3>
                    <p>Premium lakefront communities with elegant homes and established neighborhoods.</p>
                    <ul class="neighborhoods">
                        <li>Bronte</li>
                        <li>Glen Abbey</li>
                        <li>Joshua Creek</li>
                        <li>Kerr Village</li>
                        <li>Morrison</li>
                        <li>West Oak Trails</li>
                    </ul>
                    <a href="gtafencing_oakville_v1.html" class="location-link">View Oakville Services</a>
                </div>

                <div class="location-card">
                    <h3>Burlington</h3>
                    <p>Vibrant city from the lakefront to the Niagara Escarpment with diverse properties.</p>
                    <ul class="neighborhoods">
                        <li>Aldershot</li>
                        <li>Roseland</li>
                        <li>Millcroft</li>
                        <li>Alton Village</li>
                        <li>The Orchard</li>
                        <li>Downtown</li>
                    </ul>
                    <a href="gtafencing_burlington_v1.html" class="location-link">View Burlington Services</a>
                </div>

                <div class="location-card">
                    <h3>Whitby</h3>
                    <p>Growing community with family-friendly neighborhoods and new developments.</p>
                    <ul class="neighborhoods">
                        <li>Brooklin</li>
                        <li>Williamsburg</li>
                        <li>Pringle Creek</li>
                        <li>Port Whitby</li>
                        <li>Taunton North</li>
                        <li>Downtown</li>
                    </ul>
                    <a href="gtafencing_whitby_v1.html" class="location-link">View Whitby Services</a>
                </div>

                <div class="location-card">
                    <h3>Oshawa</h3>
                    <p>Historic city with diverse housing from century homes to new subdivisions.</p>
                    <ul class="neighborhoods">
                        <li>Northwood</li>
                        <li>Samac</li>
                        <li>Lakeview</li>
                        <li>Windfields</li>
                        <li>Kedron</li>
                        <li>Downtown</li>
                    </ul>
                    <a href="gtafencing_oshawa_v1.html" class="location-link">View Oshawa Services</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Coverage Section -->
    <section class="service-coverage">
        <div class="container">
            <h2 class="section-title">Complete Service Coverage</h2>
            <div class="coverage-grid">
                <div class="coverage-item">
                    <h3>Residential Services</h3>
                    <p>Pool fencing, privacy fences, decorative fencing, and property boundary solutions for homeowners across all our service areas.</p>
                </div>
                <div class="coverage-item">
                    <h3>Commercial Fencing</h3>
                    <p>Perimeter security, access control, and professional fencing solutions for businesses and institutions throughout the GTA.</p>
                </div>
                <div class="coverage-item">
                    <h3>Industrial Solutions</h3>
                    <p>Heavy-duty fencing, security systems, and specialized installations for industrial facilities and construction sites.</p>
                </div>
                <div class="coverage-item">
                    <h3>Maintenance & Repair</h3>
                    <p>Ongoing maintenance, emergency repairs, and restoration services to keep your fence in perfect condition year-round.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Local Expertise Section -->
    <section style="padding: 4rem 0; background-color: #F4F4F4;">
        <div class="container">
            <h2 class="section-title">Local Expertise You Can Trust</h2>
            <div style="max-width: 800px; margin: 0 auto; text-align: center;">
                <p style="font-size: 1.1rem; line-height: 1.8; margin-bottom: 2rem;">
                    Our deep knowledge of local building codes, municipal by-laws, and regional climate conditions ensures your fence project is completed correctly the first time. We understand the unique requirements of each community we serve.
                </p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; margin-top: 3rem;">
                    <div style="text-align: center;">
                        <h3 style="color: #273043; margin-bottom: 0.5rem;">Municipal By-Laws</h3>
                        <p>Expert knowledge of local fence regulations and permit requirements</p>
                    </div>
                    <div style="text-align: center;">
                        <h3 style="color: #273043; margin-bottom: 0.5rem;">Climate Considerations</h3>
                        <p>Materials and installation techniques suited for Southern Ontario weather</p>
                    </div>
                    <div style="text-align: center;">
                        <h3 style="color: #273043; margin-bottom: 0.5rem;">Community Standards</h3>
                        <p>Designs that complement local architectural styles and neighborhood character</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title">Ready to Get Started in Your Area?</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Contact us today for a free, no-obligation estimate tailored to your local requirements.</p>
            <div style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
                <a href="tel:6475577550" class="cta-button">Call (*************</a>
                <a href="gtafencing_contact_v1.html" class="cta-button">Request Local Quote</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:6475577550">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h3>Primary Locations</h3>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
                <div class="footer-section">
                    <h3>Additional Areas</h3>
                    <a href="gtafencing_richmond-hill_v1.html">Richmond Hill</a>
                    <a href="gtafencing_oakville_v1.html">Oakville</a>
                    <a href="gtafencing_burlington_v1.html">Burlington</a>
                    <a href="gtafencing_whitby_v1.html">Whitby</a>
                    <a href="gtafencing_oshawa_v1.html">Oshawa</a>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <a href="gtafencing_services-hub_v1.html">All Services</a>
                    <a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a>
                    <a href="gtafencing_fence-repair_v1.html">Fence Repair</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe location cards
        document.querySelectorAll('.location-card, .coverage-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add search functionality for locations
        function createLocationSearch() {
            const searchContainer = document.createElement('div');
            searchContainer.style.cssText = `
                text-align: center;
                margin-bottom: 3rem;
                padding: 2rem;
                background-color: #FFFFFF;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            `;

            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = 'Search for your city or neighborhood...';
            searchInput.style.cssText = `
                width: 100%;
                max-width: 400px;
                padding: 1rem;
                border: 2px solid #D4A537;
                border-radius: 8px;
                font-size: 1rem;
                margin-bottom: 1rem;
            `;

            searchContainer.appendChild(searchInput);

            const locationsSection = document.querySelector('.locations .container');
            locationsSection.insertBefore(searchContainer, locationsSection.querySelector('.locations-grid'));

            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const locationCards = document.querySelectorAll('.location-card');

                locationCards.forEach(card => {
                    const cityName = card.querySelector('h3').textContent.toLowerCase();
                    const neighborhoods = Array.from(card.querySelectorAll('.neighborhoods li'))
                        .map(li => li.textContent.toLowerCase());

                    const matches = cityName.includes(searchTerm) ||
                                  neighborhoods.some(neighborhood => neighborhood.includes(searchTerm));

                    card.style.display = matches ? 'block' : 'none';
                });
            });
        }

        // Initialize search when page loads
        document.addEventListener('DOMContentLoaded', createLocationSearch);
    </script>
</body>
</html>
