<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fencing Resources & Blog | GTA Fencing Company</title>
    <meta name="description" content="Expert fencing advice, maintenance tips, and industry insights from GTA Fencing Company. Learn about fence types, installation, and care.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .content-section:nth-child(even) {
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .article-card {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .article-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .article-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            color: #1B1B1F;
        }

        .article-meta {
            font-size: 0.9rem;
            color: #273043;
            margin-bottom: 1rem;
        }

        .read-more {
            color: #E94E1B;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .read-more:hover {
            color: #D4A537;
        }

        .read-more:after {
            content: "→";
            transition: transform 0.3s ease;
        }

        .read-more:hover:after {
            transform: translateX(5px);
        }

        /* FAQ Section */
        .faq-section {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .faq-grid {
            display: grid;
            gap: 1.5rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background-color: #FAF1DF;
            border-radius: 10px;
            overflow: hidden;
        }

        .faq-question {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1.5rem;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-question:hover {
            background-color: #D4A537;
        }

        .faq-answer {
            padding: 1.5rem;
            display: none;
            line-height: 1.6;
        }

        .faq-answer.active {
            display: block;
        }

        .faq-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .faq-item.active .faq-toggle {
            transform: rotate(45deg);
        }

        /* Resources Grid */
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .resource-item {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-top: 4px solid #D4A537;
        }

        .resource-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .resource-item p {
            color: #1B1B1F;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .articles-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="#resources" style="background-color: #D4A537; color: #FFFFFF;">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Fencing Resources & Expert Advice</h1>
            <p class="hero-subtitle">Discover expert insights, maintenance tips, and comprehensive guides to help you make informed decisions about your fencing project. From material selection to ongoing care, we're here to help.</p>
            <a href="#resources" class="cta-button">Explore Resources</a>
        </div>
    </section>

    <!-- Featured Articles Section -->
    <section class="content-section" id="resources">
        <div class="container">
            <h2 class="section-title">Featured Articles</h2>
            <div class="articles-grid">
                <article class="article-card">
                    <div class="article-meta">Fence Planning • 5 min read</div>
                    <h3>Choosing the Right Fence for Your Property</h3>
                    <p>A comprehensive guide to selecting the perfect fence material and style for your home, considering factors like privacy, security, maintenance, and budget.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>

                <article class="article-card">
                    <div class="article-meta">Maintenance Tips • 7 min read</div>
                    <h3>Seasonal Fence Maintenance in Ontario</h3>
                    <p>Essential maintenance tasks to protect your fence from Ontario's harsh winters and humid summers, ensuring longevity and preserving your investment.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>

                <article class="article-card">
                    <div class="article-meta">Pool Safety • 4 min read</div>
                    <h3>Pool Fence Safety Requirements in the GTA</h3>
                    <p>Understanding municipal by-laws and safety standards for pool enclosures across Toronto, Mississauga, Brampton, and surrounding areas.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>

                <article class="article-card">
                    <div class="article-meta">Installation Guide • 8 min read</div>
                    <h3>What to Expect During Fence Installation</h3>
                    <p>A step-by-step overview of the professional fence installation process, from initial consultation to final cleanup and inspection.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>

                <article class="article-card">
                    <div class="article-meta">Material Guide • 6 min read</div>
                    <h3>Wood vs. Vinyl vs. Metal: Comparing Fence Materials</h3>
                    <p>An in-depth comparison of popular fencing materials, including pros, cons, costs, and best applications for each option.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>

                <article class="article-card">
                    <div class="article-meta">Property Value • 5 min read</div>
                    <h3>How Fencing Affects Your Home's Value</h3>
                    <p>Discover how the right fence can increase your property value and curb appeal, plus tips for maximizing your return on investment.</p>
                    <a href="#" class="read-more">Read Full Article</a>
                </article>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question">
                        <span>How much does fence installation cost in the GTA?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Fence installation costs vary based on material, height, length, and site conditions. Wood fencing typically ranges from $25-45 per linear foot, vinyl from $30-60, and chain link from $15-30. We provide free, detailed estimates that include all materials and labor.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>Do I need a permit to install a fence?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Permit requirements vary by municipality. Generally, fences under 6 feet in backyards and 4 feet in front yards don't require permits, but pool fences and commercial installations often do. We handle all permit applications as part of our service.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>How long does fence installation take?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Most residential fence installations take 1-3 days, depending on length and complexity. Factors affecting timeline include weather, site preparation needs, and material type. We provide accurate timelines during your consultation.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>What's the best fence material for Ontario weather?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Vinyl and aluminum perform excellently in Ontario's climate, requiring minimal maintenance. Pressure-treated wood is also durable when properly maintained. We recommend materials based on your specific needs, budget, and aesthetic preferences.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>How do I maintain my fence?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Maintenance varies by material. Wood fences need annual cleaning and staining every 2-3 years. Vinyl requires occasional washing. Metal fences need rust inspection and touch-up painting. We offer comprehensive maintenance services for all fence types.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>Can you repair existing fences?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, we provide comprehensive fence repair services including post replacement, panel repair, gate adjustments, and storm damage restoration. We can often match existing materials and styles for seamless repairs.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Helpful Resources</h2>
            <div class="resources-grid">
                <div class="resource-item">
                    <h3>Fence Planning Calculator</h3>
                    <p>Estimate materials and costs for your fencing project with our interactive planning tool.</p>
                    <a href="#" class="cta-button" style="font-size: 1rem; padding: 0.8rem 1.5rem;">Use Calculator</a>
                </div>

                <div class="resource-item">
                    <h3>Municipal By-Laws Guide</h3>
                    <p>Quick reference for fence regulations in Toronto, Mississauga, Brampton, and other GTA municipalities.</p>
                    <a href="#" class="cta-button" style="font-size: 1rem; padding: 0.8rem 1.5rem;">View Guide</a>
                </div>

                <div class="resource-item">
                    <h3>Maintenance Schedule</h3>
                    <p>Downloadable maintenance checklist to keep your fence in perfect condition year-round.</p>
                    <a href="#" class="cta-button" style="font-size: 1rem; padding: 0.8rem 1.5rem;">Download PDF</a>
                </div>

                <div class="resource-item">
                    <h3>Design Gallery</h3>
                    <p>Browse our portfolio of completed projects for inspiration and ideas for your property.</p>
                    <a href="#" class="cta-button" style="font-size: 1rem; padding: 0.8rem 1.5rem;">View Gallery</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title">Need Expert Advice?</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Our fencing experts are here to answer your questions and help you plan your perfect fence.</p>
            <div style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
                <a href="tel:6475577550" class="cta-button">Call (*************</a>
                <a href="gtafencing_contact_v1.html" class="cta-button">Schedule Consultation</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:6475577550">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h3>Popular Articles</h3>
                    <a href="#">Choosing the Right Fence</a>
                    <a href="#">Pool Fence Safety</a>
                    <a href="#">Seasonal Maintenance</a>
                    <a href="#">Installation Process</a>
                    <a href="#">Material Comparison</a>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <a href="gtafencing_services-hub_v1.html">All Services</a>
                    <a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a>
                    <a href="gtafencing_fence-repair_v1.html">Fence Repair</a>
                </div>
                <div class="footer-section">
                    <h3>Service Areas</h3>
                    <a href="gtafencing_location-hub_v1.html">All Locations</a>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // FAQ Toggle Functionality
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const faqItem = this.parentElement;
                const answer = faqItem.querySelector('.faq-answer');
                const toggle = this.querySelector('.faq-toggle');

                // Close other open FAQs
                document.querySelectorAll('.faq-item').forEach(item => {
                    if (item !== faqItem) {
                        item.classList.remove('active');
                        item.querySelector('.faq-answer').classList.remove('active');
                        item.querySelector('.faq-toggle').textContent = '+';
                    }
                });

                // Toggle current FAQ
                faqItem.classList.toggle('active');
                answer.classList.toggle('active');
                toggle.textContent = faqItem.classList.contains('active') ? '×' : '+';
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe article cards and resource items
        document.querySelectorAll('.article-card, .resource-item, .faq-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Search functionality for articles
        function createArticleSearch() {
            const searchContainer = document.createElement('div');
            searchContainer.style.cssText = `
                text-align: center;
                margin-bottom: 3rem;
                padding: 2rem;
                background-color: #FFFFFF;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            `;

            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = 'Search articles and resources...';
            searchInput.style.cssText = `
                width: 100%;
                max-width: 400px;
                padding: 1rem;
                border: 2px solid #D4A537;
                border-radius: 8px;
                font-size: 1rem;
            `;

            searchContainer.appendChild(searchInput);

            const articlesSection = document.querySelector('#resources .container');
            articlesSection.insertBefore(searchContainer, articlesSection.querySelector('.articles-grid'));

            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const articleCards = document.querySelectorAll('.article-card');

                articleCards.forEach(card => {
                    const title = card.querySelector('h3').textContent.toLowerCase();
                    const content = card.querySelector('p').textContent.toLowerCase();
                    const meta = card.querySelector('.article-meta').textContent.toLowerCase();

                    const matches = title.includes(searchTerm) ||
                                  content.includes(searchTerm) ||
                                  meta.includes(searchTerm);

                    card.style.display = matches ? 'block' : 'none';
                });
            });
        }

        // Initialize search when page loads
        document.addEventListener('DOMContentLoaded', createArticleSearch);
    </script>
</body>
</html>
