<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vinyl Fencing | GTA Fencing Company</title>
    <meta name="description" content="Low-maintenance vinyl fencing in the GTA. Durable, beautiful vinyl fences that won't rot, fade, or peel. Free estimates available.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .content-section:nth-child(even) {
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .option-card {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 4px solid #D4A537;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left-color: #E94E1B;
        }

        .option-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .option-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            color: #1B1B1F;
        }

        .option-card .features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .option-card .features li {
            padding: 0.3rem 0;
            color: #273043;
            position: relative;
            padding-left: 1.5rem;
        }

        .option-card .features li:before {
            content: "✓";
            color: #D4A537;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        /* Process Steps */
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .step-card {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .step-number {
            background-color: #E94E1B;
            color: #FFFFFF;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .step-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .step-card p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Locations Grid */
        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .location-item {
            background-color: #FFFFFF;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .location-item:hover {
            background-color: #D4A537;
            color: #FFFFFF;
            transform: translateY(-3px);
        }

        .location-item a {
            text-decoration: none;
            color: inherit;
            font-weight: 500;
        }

        /* Why Choose Section */
        .why-choose {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            background-color: #FAF1DF;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }

        .feature-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .feature-item p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .options-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Low-Maintenance Vinyl Fencing in the Greater Toronto Area</h1>
            <p class="hero-subtitle">Choose a durable, beautiful, and virtually maintenance-free vinyl fence from GTA Fencing Company. We install premium vinyl fencing across the GTA that won't rot, fade, or peel. Explore our wide range of styles and colors to find the perfect long-term solution for your property.</p>
            <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Vinyl Fence Quote</a>
        </div>
    </section>

    <!-- Vinyl Fencing Options Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Our Premium Vinyl Fencing Selection</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                Modern vinyl offers incredible versatility in design, from total privacy to classic picket styles.
            </p>
            <div class="options-grid">
                <div class="option-card">
                    <h3>Vinyl Privacy Fencing</h3>
                    <p>Complete privacy with solid panels that block views and reduce noise from neighbors and street traffic.</p>
                    <ul class="features">
                        <li>6-8 foot height options</li>
                        <li>Tongue and groove panels</li>
                        <li>No gaps between boards</li>
                        <li>Multiple color choices</li>
                        <li>Wind-resistant design</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Semi-Privacy Vinyl Fencing</h3>
                    <p>Stylish fencing with spacing between boards for airflow while maintaining most of your privacy.</p>
                    <ul class="features">
                        <li>Spaced picket design</li>
                        <li>Enhanced air circulation</li>
                        <li>Reduced wind load</li>
                        <li>Decorative top options</li>
                        <li>Cost-effective solution</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Vinyl Picket Fencing</h3>
                    <p>Classic picket fence charm with modern vinyl durability, perfect for front yards and gardens.</p>
                    <ul class="features">
                        <li>Traditional picket styling</li>
                        <li>Pointed or flat top pickets</li>
                        <li>3-4 foot standard heights</li>
                        <li>Decorative post caps</li>
                        <li>Timeless curb appeal</li>
                    </ul>
                </div>

                <div class="option-card">
                    <h3>Vinyl Post and Rail Fencing</h3>
                    <p>Open rail design perfect for property boundaries, horse paddocks, and decorative applications.</p>
                    <ul class="features">
                        <li>2, 3, or 4 rail configurations</li>
                        <li>Ranch-style appearance</li>
                        <li>Easy installation</li>
                        <li>Minimal maintenance required</li>
                        <li>Excellent for large areas</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Process Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">How Our Vinyl Fence Installation Process Works</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                Our certified process ensures your vinyl fence is installed securely for maximum durability and a flawless look.
            </p>
            <div class="process-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3>Free On-Site Estimate</h3>
                    <p><strong>Precise measurements and style consultation</strong> → A clear, all-inclusive quote for your project.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3>Property Line Verification</h3>
                    <p><strong>Confirming boundaries and underground utilities</strong> → A safe and accurate installation process.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3>Professional Installation</h3>
                    <p><strong>Setting posts in concrete and expertly assembling panels</strong> → A strong, level fence that withstands high winds.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3>Gate and Hardware Fitting</h3>
                    <p><strong>Installation of matching gates with high-quality hinges and latches</strong> → Secure and reliable access points.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">5</div>
                    <h3>Final Quality Check</h3>
                    <p><strong>Walkthrough to ensure every panel and post is perfect</strong> → Your complete satisfaction is our guarantee.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Areas Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Vinyl Fencing Coverage Across the Greater Toronto Area</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                We install premium vinyl fences in communities throughout the region.
            </p>
            <div class="locations-grid">
                <div class="location-item">
                    <a href="gtafencing_toronto_v1.html">Vinyl Fencing in Toronto</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_vaughan_v1.html">Vinyl Fencing in Vaughan</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_richmond-hill_v1.html">Vinyl Fencing in Richmond Hill</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_markham_v1.html">Vinyl Fencing in Markham</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_mississauga_v1.html">Vinyl Fencing in Mississauga</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_brampton_v1.html">Vinyl Fencing in Brampton</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_oakville_v1.html">Vinyl Fencing in Oakville</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_burlington_v1.html">Vinyl Fencing in Burlington</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_whitby_v1.html">Vinyl Fencing in Whitby</a>
                </div>
                <div class="location-item">
                    <a href="gtafencing_oshawa_v1.html">Vinyl Fencing in Oshawa</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose">
        <div class="container">
            <h2 class="section-title">The GTA Fencing Company Advantage for Vinyl Fences</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                Choose us for a superior product and a lifetime of value.
            </p>
            <div class="features-grid">
                <div class="feature-item">
                    <h3>Manufacturer-Certified Installers</h3>
                    <p>Our team is certified by leading vinyl fence manufacturers, ensuring proper installation techniques and warranty compliance.</p>
                </div>
                <div class="feature-item">
                    <h3>Industry-Leading Material Warranties</h3>
                    <p>We offer comprehensive warranties on both materials and workmanship, giving you peace of mind for years to come.</p>
                </div>
                <div class="feature-item">
                    <h3>Free, No-Obligation Quotes</h3>
                    <p>Get a detailed estimate with no hidden fees or surprises. We provide transparent pricing for all vinyl fencing projects.</p>
                </div>
                <div class="feature-item">
                    <h3>Expert Color and Style Consultation</h3>
                    <p>Our design experts help you choose the perfect vinyl fence style and color to complement your home's architecture.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Location Links Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Vinyl Fencing Installation Across the Greater Toronto Area</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                We provide professional vinyl fencing services throughout the GTA. Click below to learn about our services in your area.
            </p>
            <div class="locations-grid">
                <div class="location-item">
                    <h3><a href="gtafencing_toronto_v1.html">Vinyl Fencing in Toronto</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_vaughan_v1.html">Vinyl Fencing in Vaughan</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_richmond-hill_v1.html">Vinyl Fencing in Richmond Hill</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_markham_v1.html">Vinyl Fencing in Markham</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_mississauga_v1.html">Vinyl Fencing in Mississauga</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_brampton_v1.html">Vinyl Fencing in Brampton</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_oakville_v1.html">Vinyl Fencing in Oakville</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_burlington_v1.html">Vinyl Fencing in Burlington</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_whitby_v1.html">Vinyl Fencing in Whitby</a></h3>
                </div>
                <div class="location-item">
                    <h3><a href="gtafencing_oshawa_v1.html">Vinyl Fencing in Oshawa</a></h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Services Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Explore Our Full Range of Fencing Services</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem;">
                We are the GTA's one-stop shop for all residential and commercial fencing needs.
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Residential Fencing Solutions</h3>
                    <p style="margin-bottom: 1.5rem;">Pool fencing, wood fencing, and decorative options for homeowners.</p>
                    <a href="gtafencing_services-hub_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">View All Residential Services →</a>
                </div>
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Commercial and Industrial Fencing</h3>
                    <p style="margin-bottom: 1.5rem;">Security fencing, perimeter solutions, and access control systems.</p>
                    <a href="gtafencing_services-hub_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">View Commercial Services →</a>
                </div>
                <div style="background-color: #FFFFFF; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
                    <h3 style="color: #273043; margin-bottom: 1rem;">Fence Maintenance and Repair</h3>
                    <p style="margin-bottom: 1.5rem;">Keep your existing fences in perfect condition with our maintenance services.</p>
                    <a href="gtafencing_fence-maintenance_v1.html" style="color: #E94E1B; text-decoration: none; font-weight: 600;">Learn About Maintenance →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title">Ready for Your Vinyl Fence?</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Contact us today for a free, no-obligation estimate on your vinyl fencing project.</p>
            <div style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
                <a href="tel:6475577550" class="cta-button">Call (*************</a>
                <a href="gtafencing_contact_v1.html" class="cta-button">Request Vinyl Fence Quote</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p>Phone: <a href="tel:6475577550">(*************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h3>Vinyl Fencing Services</h3>
                    <a href="#">Privacy Vinyl Fencing</a>
                    <a href="#">Semi-Privacy Vinyl Fencing</a>
                    <a href="#">Vinyl Picket Fencing</a>
                    <a href="#">Post and Rail Fencing</a>
                    <a href="#">Vinyl Gate Installation</a>
                </div>
                <div class="footer-section">
                    <h3>Other Services</h3>
                    <a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a>
                    <a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a>
                    <a href="gtafencing_chain-link-fencing_v1.html">Chain Link Fencing</a>
                    <a href="gtafencing_fence-repair_v1.html">Fence Repair</a>
                    <a href="gtafencing_fence-maintenance_v1.html">Fence Maintenance</a>
                </div>
                <div class="footer-section">
                    <h3>Service Areas</h3>
                    <a href="gtafencing_toronto_v1.html">Toronto</a>
                    <a href="gtafencing_mississauga_v1.html">Mississauga</a>
                    <a href="gtafencing_brampton_v1.html">Brampton</a>
                    <a href="gtafencing_vaughan_v1.html">Vaughan</a>
                    <a href="gtafencing_markham_v1.html">Markham</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GTA Fencing Company. All rights reserved. Licensed and insured vinyl fence contractor serving the Greater Toronto Area.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe cards and elements
        document.querySelectorAll('.option-card, .step-card, .location-item, .feature-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add hover effects to option cards
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderLeftColor = '#E94E1B';
            });

            card.addEventListener('mouseleave', function() {
                this.style.borderLeftColor = '#D4A537';
            });
        });
    </script>
</body>
</html>
