<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GTA Fencing Company - Fence Contractor - Toronto, Ontario</title>
    <meta name="description" content="Professional fence installation and repair in Toronto. Serving all Toronto neighborhoods with expert fencing services. Free estimates available.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
            line-height: 1.6;
            color: #1B1B1F;
            background-color: #FFFFFF;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #D4A537;
        }

        .contact-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .phone {
            font-size: 1.2rem;
            font-weight: 600;
            color: #E94E1B;
            text-decoration: none;
        }

        .phone:hover {
            color: #D4A537;
        }

        .nav {
            background-color: #FAF1DF;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-menu a {
            text-decoration: none;
            color: #273043;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            background-color: #D4A537;
            color: #FFFFFF;
        }

        .cta-button {
            display: inline-block;
            background-color: #E94E1B;
            color: #FFFFFF;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 200px;
            min-height: 44px;
        }

        .cta-button:hover {
            background-color: #D4A537;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(233, 78, 27, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #273043 0%, #1B1B1F 100%);
            color: #FFFFFF;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #FAF1DF;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .content-section:nth-child(even) {
            background-color: #F4F4F4;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #273043;
            text-align: center;
            margin-bottom: 3rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-category {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #D4A537;
        }

        .service-category h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1.5rem;
        }

        .service-category ul {
            list-style: none;
        }

        .service-category li {
            margin-bottom: 0.8rem;
        }

        .service-category a {
            color: #E94E1B;
            text-decoration: none;
            font-weight: 500;
        }

        .service-category a:hover {
            color: #D4A537;
        }

        /* Local Features */
        .local-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            background-color: #FFFFFF;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .feature-item h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #273043;
            margin-bottom: 1rem;
        }

        .feature-item p {
            color: #1B1B1F;
            line-height: 1.6;
        }

        /* Neighborhoods */
        .neighborhoods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .neighborhood-item {
            background-color: #FFFFFF;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .neighborhood-item:hover {
            background-color: #D4A537;
            color: #FFFFFF;
            transform: translateY(-3px);
        }

        /* FAQ Section */
        .faq-section {
            padding: 4rem 0;
            background-color: #FFFFFF;
        }

        .faq-grid {
            display: grid;
            gap: 1.5rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background-color: #FAF1DF;
            border-radius: 10px;
            overflow: hidden;
        }

        .faq-question {
            background-color: #273043;
            color: #FFFFFF;
            padding: 1.5rem;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-question:hover {
            background-color: #D4A537;
        }

        .faq-answer {
            padding: 1.5rem;
            display: none;
            line-height: 1.6;
        }

        .faq-answer.active {
            display: block;
        }

        .faq-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .faq-item.active .faq-toggle {
            transform: rotate(45deg);
        }

        /* Contact Section */
        .contact {
            padding: 4rem 0;
            background-color: #273043;
            color: #FFFFFF;
            text-align: center;
        }

        .contact h2 {
            color: #FFFFFF;
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            background-color: #1B1B1F;
            color: #FAF1DF;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #D4A537;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #FAF1DF;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: #E94E1B;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #273043;
            color: #FAF1DF;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Focus styles */
        a:focus,
        button:focus {
            outline: 2px solid #D4A537;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">GTA Fencing</div>
                <div class="contact-info">
                    <a href="tel:6475577550" class="phone" aria-label="Call us at ************">(*************</a>
                    <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Quote</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="gtafencing_homepage_v1.html">Home</a></li>
                <li><a href="gtafencing_services-hub_v1.html">Services</a></li>
                <li><a href="gtafencing_location-hub_v1.html">Locations</a></li>
                <li><a href="gtafencing_blog_v1.html">Resources</a></li>
                <li><a href="gtafencing_contact_v1.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Toronto's Trusted Fence Contractor for Quality Fencing</h1>
            <p class="hero-subtitle">We offer our full range of professional fencing services to homeowners and businesses throughout Toronto. Our local expertise ensures your project complies with all municipal by-laws, from heritage district requirements to pool enclosure regulations, and is built to withstand Southern Ontario's demanding weather.</p>
            <a href="gtafencing_contact_v1.html" class="cta-button">Get Free Toronto Quote</a>
        </div>
    </section>

    <!-- Expert Fencing Services Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Expert Fencing Installation and Repair for Toronto Properties</h2>
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                From Scarborough to Etobicoke, North York to the Harbourfront, we provide comprehensive fencing services that meet Toronto's unique requirements.
            </p>

            <div class="services-grid">
                <div class="service-category">
                    <h3>Residential Fencing in Toronto</h3>
                    <ul>
                        <li><a href="gtafencing_pool-fencing_v1.html">Pool Fencing</a></li>
                        <li><a href="gtafencing_wood-fencing_v1.html">Wood Fencing</a></li>
                        <li><a href="gtafencing_vinyl-fencing_v1.html">Vinyl Fencing</a></li>
                        <li><a href="gtafencing_chain-link-fencing_v1.html">Chain Link Fencing</a></li>
                        <li><a href="gtafencing_wrought-iron-fencing_v1.html">Wrought Iron Fencing</a></li>
                    </ul>
                </div>

                <div class="service-category">
                    <h3>Commercial Fencing in Toronto</h3>
                    <ul>
                        <li><a href="gtafencing_perimeter-fencing_v1.html">Perimeter Fencing</a></li>
                        <li><a href="gtafencing_security-fencing_v1.html">Security Fencing</a></li>
                        <li><a href="gtafencing_industrial-chain-link_v1.html">Industrial Chain Link Fencing</a></li>
                    </ul>
                </div>

                <div class="service-category">
                    <h3>Fence Maintenance and Repair in Toronto</h3>
                    <ul>
                        <li><a href="gtafencing_fence-maintenance_v1.html">Fence Maintenance</a></li>
                        <li><a href="gtafencing_fence-repair_v1.html">Fence Repair Services</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Local Expertise Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Why Choose Us for Toronto Fencing Projects</h2>
            <div class="local-features">
                <div class="feature-item">
                    <h3>Service Availability in Toronto</h3>
                    <p><strong>Full coverage from Scarborough to Etobicoke, North York to the Harbourfront</strong> → Prompt response times for quotes and emergency repairs.</p>
                </div>

                <div class="feature-item">
                    <h3>Toronto By-Laws & Regulations</h3>
                    <p><strong>Deep knowledge of Toronto's specific fence height rules (e.g., front yard vs. backyard) and material restrictions in heritage zones</strong> → Guaranteed code-compliant installation for your peace of mind.</p>
                </div>

                <div class="feature-item">
                    <h3>Area-Specific Challenges</h3>
                    <p><strong>Solutions for narrow urban lots, shared property lines, and challenging soil conditions</strong> → Our local experience provides a tailored, durable fencing solution.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Local Areas Section -->
    <section class="content-section">
        <div class="container">
            <h2 class="section-title">Local Areas We Serve Near Toronto</h2>
            <div class="neighborhoods-grid">
                <div class="neighborhood-item">North York</div>
                <div class="neighborhood-item">Scarborough</div>
                <div class="neighborhood-item">Etobicoke</div>
                <div class="neighborhood-item">The Beaches</div>
                <div class="neighborhood-item">Leaside</div>
                <div class="neighborhood-item">Rosedale</div>
                <div class="neighborhood-item">Forest Hill</div>
                <div class="neighborhood-item">Downtown Toronto</div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions about Fencing in Toronto</h2>
            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question">
                        <span>Do I need a permit to build a fence in Toronto?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Generally, fences under 6 feet in backyards and 4 feet in front yards don't require permits in Toronto. However, pool fences and fences in heritage districts may have special requirements. We handle all permit applications as part of our service.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>What are the rules for a fence bordering a public laneway in Toronto?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Fences along public laneways in Toronto must maintain specific setbacks and height restrictions. The City requires clear sight lines at intersections and may have additional requirements for corner properties.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span>Are there specific fence height restrictions for front yards in Toronto?</span>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, Toronto generally limits front yard fences to 4 feet in height. However, heritage districts and specific zoning areas may have different requirements. We ensure your fence meets all local regulations.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
